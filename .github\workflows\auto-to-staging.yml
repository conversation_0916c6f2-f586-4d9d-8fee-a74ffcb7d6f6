name: Auto-merge every PR into local

on:
  pull_request_target:          # gira anche per PR da fork
    branches: [local]
    types: [opened, synchronize, reopened]

permissions:
  contents: write               # serve per il push su local
  pull-requests: write          # consente di chiudere PR via API

jobs:
  merge_to_local:
    runs-on: windows-latest                                   # così usiamo PowerShell
    concurrency: local-merges             # un merge alla volta
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}  # checkout del ramo PR
          fetch-depth: 0

      - name: Merge PR into local
        shell: pwsh
        env:
          GIT_AUTH: https://x-access-token:${{ secrets.AUTO_MERGE_TOKEN }}@github.com/${{ github.repository }}
        run: |
          git config user.name  "auto-staging-bot"
          git config user.email "<EMAIL>"

          git fetch origin local --depth=1
          git checkout -B local origin/local    # switch a local
          git merge --no-ff $env:GITHUB_SHA -m "chore(local): merge PR #${{ github.event.number }}"
          git push $env:GIT_AUTH local            # push su local

      - name: Merge PR via API
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            await github.rest.pulls.merge({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.payload.pull_request.number,
              merge_method: 'merge'
            })