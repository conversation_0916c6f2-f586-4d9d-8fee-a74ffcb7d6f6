# Supabase Database Migrations - CreateBillOnline

## Project Information
- **Project ID**: wzkobaqpkxayifiiscvg
- **Project Name**: createbillonline
- **Region**: eu-central-2
- **Status**: ACTIVE_HEALTHY
- **Database Version**: PostgreSQL 17.4.1.054

## Environment Configuration
The following environment variables have been configured in `.env.local`:

```env
NEXT_PUBLIC_SUPABASE_URL=https://wzkobaqpkxayifiiscvg.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.471WTAy8PgqeaqlU-s_Y6uBRexS28y73wFqJG8zbY0w
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.KKMKJ4fWxAa0e_QwiTO-sd4Mp3SOj5WSEYzCdspKCKQ
```

## Database Schema

### 1. user_profiles Table
**Purpose**: Store user profile information for authenticated users

```sql
CREATE TABLE user_profiles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name TEXT,
  company_name TEXT,
  address TEXT,
  phone TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);
```

**Columns**:
- `id`: Primary key (UUID)
- `user_id`: Foreign key to auth.users table (UUID)
- `full_name`: User's full name (TEXT, nullable)
- `company_name`: Company name (TEXT, nullable)
- `address`: User's address (TEXT, nullable)
- `phone`: Phone number (TEXT, nullable)
- `created_at`: Record creation timestamp
- `updated_at`: Record last update timestamp

### 2. saved_invoices Table
**Purpose**: Store saved invoice data for authenticated users

```sql
CREATE TABLE saved_invoices (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  invoice_data JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Columns**:
- `id`: Primary key (UUID)
- `user_id`: Foreign key to auth.users table (UUID)
- `title`: Invoice title (TEXT, required)
- `invoice_data`: Complete invoice data in JSON format (JSONB, required)
- `created_at`: Record creation timestamp
- `updated_at`: Record last update timestamp

### 3. articles Table
**Purpose**: Store blog articles (migrated from external PostgreSQL)

```sql
CREATE TABLE articles (
  id SERIAL PRIMARY KEY,
  title TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  content TEXT NOT NULL,
  excerpt TEXT,
  image_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Columns**:
- `id`: Primary key (SERIAL)
- `title`: Article title (TEXT, required)
- `slug`: URL-friendly slug (TEXT, required, unique)
- `content`: Article content (TEXT, required)
- `excerpt`: Article excerpt (TEXT, nullable)
- `image_url`: Featured image URL (TEXT, nullable)
- `created_at`: Record creation timestamp
- `updated_at`: Record last update timestamp

## Row Level Security (RLS) Policies

### user_profiles Policies
1. **Users can view own profile** (SELECT): `auth.uid() = user_id`
2. **Users can insert own profile** (INSERT): `auth.uid() = user_id`
3. **Users can update own profile** (UPDATE): `auth.uid() = user_id`
4. **Users can delete own profile** (DELETE): `auth.uid() = user_id`

### saved_invoices Policies
1. **Users can view own invoices** (SELECT): `auth.uid() = user_id`
2. **Users can insert own invoices** (INSERT): `auth.uid() = user_id`
3. **Users can update own invoices** (UPDATE): `auth.uid() = user_id`
4. **Users can delete own invoices** (DELETE): `auth.uid() = user_id`

## Database Indexes

### Performance Indexes Created
1. `idx_user_profiles_user_id` on `user_profiles(user_id)`
2. `idx_saved_invoices_user_id` on `saved_invoices(user_id)`
3. `idx_saved_invoices_created_at` on `saved_invoices(created_at DESC)`
4. `idx_articles_slug` on `articles(slug)`

## Database Functions and Triggers

### Auto-Update Function
```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';
```

### Triggers
1. `update_user_profiles_updated_at` - Auto-updates `updated_at` on user_profiles
2. `update_saved_invoices_updated_at` - Auto-updates `updated_at` on saved_invoices
3. `update_articles_updated_at` - Auto-updates `updated_at` on articles

## Migration Status
✅ **COMPLETED** - All database migrations have been successfully applied

### Migration Summary:
- [x] Created user_profiles table
- [x] Created saved_invoices table
- [x] Enabled Row Level Security on both tables
- [x] Created all necessary RLS policies
- [x] Created performance indexes
- [x] Created auto-update triggers
- [x] Configured environment variables
- [x] Verified build compilation
- [x] Tested database connectivity

## Next Steps
The database is now fully configured and ready for use. The application can:
1. Register and authenticate users
2. Store and retrieve user profiles
3. Save and manage user invoices
4. Enforce proper data security through RLS policies

## Testing
All migrations have been tested and verified:
- Build compilation: ✅ SUCCESS
- Database connectivity: ✅ SUCCESS
- Table creation: ✅ SUCCESS
- RLS policies: ✅ SUCCESS
- Indexes: ✅ SUCCESS
- Triggers: ✅ SUCCESS
