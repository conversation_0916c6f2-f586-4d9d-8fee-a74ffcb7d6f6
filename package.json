{"name": "createbillonline", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@sparticuz/chromium": "^133.0.0", "@supabase/supabase-js": "^2.51.0", "@types/dompurify": "^3.0.5", "@vercel/analytics": "^1.5.0", "dompurify": "^3.2.6", "highlight.js": "^11.11.1", "marked": "^15.0.12", "next": "15.3.2", "nodemailer": "^6.10.1", "puppeteer-core": "^24.9.0", "react": "^19.0.0", "react-dom": "^19.0.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.2", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "puppeteer": "^24.9.0", "tailwindcss": "^4", "typescript": "^5"}}