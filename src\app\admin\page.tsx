'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function AdminPage() {
  const router = useRouter();
  const [token, setToken] = useState('');
  const [title, setTitle] = useState('');
  const [slug, setSlug] = useState('');
  const [content, setContent] = useState('');
  const [excerpt, setExcerpt] = useState('');
  const [imageUrl, setImageUrl] = useState('');
  const [status, setStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');

  useEffect(() => {
    const stored = localStorage.getItem('adminToken');
    if (!stored) {
      router.push('/login');
    } else {
      setToken(stored);
    }
  }, [router]);

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    setStatus('saving');
    try {
      const res = await fetch('/api/articles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ title, slug, content, excerpt, image_url: imageUrl }),
      });
      if (res.ok) {
        setStatus('saved');
        setTitle('');
        setSlug('');
        setContent('');
        setExcerpt('');
        setImageUrl('');
      } else {
        setStatus('error');
      }
    } catch {
      setStatus('error');
    }
  }

  return (
    <div className="w-full max-w-2xl mx-auto py-12 px-4">
      <h1 className="text-4xl font-extrabold mb-8 text-center text-indigo-700">New Article</h1>
      <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg shadow-lg flex flex-col gap-4">
        <input
          type="text"
          placeholder="Title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          className="border border-gray-300 bg-white text-gray-900 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
          required
        />
        <input
          type="text"
          placeholder="Slug"
          value={slug}
          onChange={(e) => setSlug(e.target.value)}
          className="border border-gray-300 bg-white text-gray-900 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
          required
        />
        <input
          type="text"
          placeholder="Excerpt"
          value={excerpt}
          onChange={(e) => setExcerpt(e.target.value)}
          className="border border-gray-300 bg-white text-gray-900 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
        />
        <input
          type="text"
          placeholder="Image URL"
          value={imageUrl}
          onChange={(e) => setImageUrl(e.target.value)}
          className="border border-gray-300 bg-white text-gray-900 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
        />
        <textarea
          placeholder="Content (markdown allowed)"
          value={content}
          onChange={(e) => setContent(e.target.value)}
          className="border border-gray-300 bg-white text-gray-900 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
          rows={8}
          required
        />
        <button
          type="submit"
          disabled={status === 'saving'}
          className="bg-indigo-600 hover:bg-indigo-700 text-white rounded-md px-4 py-2 font-semibold disabled:opacity-50 transition-colors"
        >
          Save
        </button>
      </form>
      {status === 'saved' && (
        <p className="text-green-600 mt-4 text-center font-medium">Article saved.</p>
      )}
      {status === 'error' && (
        <p className="text-red-600 mt-4 text-center font-medium">Error saving article.</p>
      )}
    </div>
  );
}
