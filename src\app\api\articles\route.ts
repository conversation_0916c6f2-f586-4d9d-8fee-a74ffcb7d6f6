import { NextRequest, NextResponse } from 'next/server';
import { getArticles, createArticle } from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const offset = parseInt(searchParams.get('offset') || '0', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const articles = await getArticles(offset, limit);
    return NextResponse.json({ articles });
  } catch (e) {
    console.error('[API/articles]', e);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  const auth = req.headers.get('authorization');
  if (auth !== `Bearer ${process.env.ADMIN_TOKEN}`) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  try {
    const { title, slug, content, excerpt, image_url } = await req.json();
    if (!title || !slug || !content) {
      return NextResponse.json({ error: 'Missing fields' }, { status: 400 });
    }
    const article = await createArticle(title, slug, content, excerpt, image_url);
    return NextResponse.json({ article }, { status: 201 });
  } catch (e) {
    console.error('[API/articles]', e);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}
