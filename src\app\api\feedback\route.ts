import { NextRequest, NextResponse } from "next/server";
import nodemailer from "nodemailer";

export async function POST(req: NextRequest) {
  try {
    const { name, email, message } = await req.json();
    if (!message) {
      return NextResponse.json({ error: "Message required" }, { status: 400 });
    }

    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: Number(process.env.SMTP_PORT) || 587,
      secure: process.env.SMTP_SECURE === "true",
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });

    await transporter.sendMail({
      from: process.env.SMTP_FROM || process.env.SMTP_USER,
      to: "<EMAIL>",
      subject: "Nuovo feedback su CreateBillOnline",
      text: `Da: ${name || "Anonimo"} (${email || "N/A"})\n\n${message}`,
    });

    return NextResponse.json({ ok: true });
  } catch (e) {
    console.error("[API/feedback]", e);
    return NextResponse.json(
      { error: "Errore invio feedback" },
      { status: 500 }
    );
  }
}
