import type { InvoiceData } from "../../../store/invoiceStore";
import { NextRequest, NextResponse } from "next/server";
import { generatePdf } from "../../../lib/pdf/generatePdf";

export const runtime = "nodejs";

export async function POST(req: NextRequest) {
  try {
    const body = (await req.json()) as { data: InvoiceData; layout: string };
    const pdfBuffer = await generatePdf(body.data, body.layout);
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": "attachment; filename=invoice.pdf",
      },
    });
  } catch (e: unknown) {
    const error = e instanceof Error ? e : new Error(String(e));
    console.error("[API/generate] PDF generation error", error);
    return NextResponse.json(
      { error: "Errore generazione PDF", details: error.message },
      { status: 500 }
    );
  }
}
