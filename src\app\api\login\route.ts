import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const { username, password } = await req.json();

    // Simple admin credentials - in production, use proper authentication
    const adminUsername = process.env.ADMIN_USERNAME || 'admin';
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';

    if (username === adminUsername && password === adminPassword) {
      return NextResponse.json({ token: process.env.ADMIN_TOKEN });
    }
    return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 });
  } catch (e) {
    console.error('[API/login]', e);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}
