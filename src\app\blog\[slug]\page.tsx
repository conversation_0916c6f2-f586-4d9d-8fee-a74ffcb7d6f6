import { notFound } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { getArticleBySlug, getLatestArticles } from '@/lib/db';
import { markdownToHtml } from '@/lib/markdown';
import ThemeToggle from '@/components/ThemeToggle';

// Add highlight.js CSS for code syntax highlighting
import 'highlight.js/styles/atom-one-dark.css';

export const dynamic = 'force-dynamic';

interface PageProps {
  params: Promise<{ slug: string }>;
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function ArticlePage({ params }: PageProps) {
  const { slug } = await params;
  const article = await getArticleBySlug(slug);
  if (!article) return notFound();

  const suggestions = await getLatestArticles(3, article.id);
  const formattedDate = article.created_at ? new Date(article.created_at).toLocaleDateString('it-IT', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }) : null;

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto flex justify-end mb-4">
        <ThemeToggle />
      </div>
      <article className="max-w-3xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        {/* Article Header with Cover Image */}
        <div className="relative h-[300px] w-full bg-gradient-to-r from-indigo-600 to-purple-600">
          {article.image_url ? (
            <Image 
              src={article.image_url} 
              fill 
              className="object-cover" 
              alt={article.title} 
              priority 
            />
          ) : (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-white text-3xl font-bold">{article.title.charAt(0)}</div>
            </div>
          )}
        </div>

        {/* Article Content */}
        <div className="px-6 pt-8 pb-12 sm:px-10">
          {/* Metadata */}
          <div className="flex items-center mb-6 text-sm text-gray-500 dark:text-gray-400">
            {formattedDate && (
              <span className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                {formattedDate}
              </span>
            )}
          </div>
          
          {/* Title */}
          <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-6 leading-tight">{article.title}</h1>
          
          {/* Content */}
          <div className="article-content" dangerouslySetInnerHTML={{ __html: markdownToHtml(article.content) }} />
        </div>
      </article>

      {/* Article Navigation and Suggestions */}
      <div className="max-w-3xl mx-auto mt-12 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100 border-b pb-4 dark:border-gray-700">Altri articoli</h2>
        
        <div className="grid gap-6 md:grid-cols-3">
          {suggestions.map((s) => (
            <Link 
              href={`/blog/${s.slug}`} 
              key={s.id}
              className="block bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition rounded-lg overflow-hidden shadow hover:shadow-md"
            >
              <div className="h-32 bg-gradient-to-r from-indigo-500 to-purple-500 relative">
                {s.image_url ? (
                  <Image 
                    src={s.image_url} 
                    fill
                    className="object-cover" 
                    alt={s.title} 
                  />
                ) : null}
              </div>
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 dark:text-gray-100 hover:text-indigo-700 transition">{s.title}</h3>
                {s.excerpt && <p className="text-gray-600 dark:text-gray-400 text-sm mt-2 line-clamp-2">{s.excerpt}</p>}
              </div>
            </Link>
          ))}
        </div>
        
        {/* Back to all articles link */}
        <div className="mt-8 text-center">
          <Link 
            href="/blog" 
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Tutti gli articoli
          </Link>
        </div>
      </div>
    </div>
  );
}
