import ArticleList from '@/components/ArticleList';
import { getArticles } from '@/lib/db';
import ThemeToggle from '@/components/ThemeToggle';

export const dynamic = 'force-dynamic';

export default async function BlogPage() {
  const articles = await getArticles(0, 10);
  return (
    <div className="max-w-2xl mx-auto py-10 px-4">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-4xl font-bold">Blog</h1>
        <ThemeToggle />
      </div>
      <ArticleList initialArticles={articles} />
    </div>
  );
}
