"use client";
import { useState } from "react";

export default function FeedbackPage() {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [message, setMessage] = useState("");
  const [status, setStatus] = useState<"idle" | "sending" | "sent" | "error">("idle");

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    setStatus("sending");
    try {
      const res = await fetch("/api/feedback", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name, email, message }),
      });
      if (res.ok) {
        setStatus("sent");
        setName("");
        setEmail("");
        setMessage("");
      } else {
        setStatus("error");
      }
    } catch {
      setStatus("error");
    }
  }

  return (
    <div className="w-full max-w-2xl mx-auto py-12 px-4">
      <h1 className="text-4xl font-extrabold mb-8 text-center text-indigo-700">
        Leave feedback
      </h1>
      <form
        onSubmit={handleSubmit}
        className="bg-white p-6 rounded-lg shadow-lg flex flex-col gap-4"
      >
        <input
          type="text"
          placeholder="Name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          className="border border-gray-300 bg-white dark:bg-white text-gray-900 placeholder-gray-500 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
        />
        <input
          type="email"
          placeholder="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="border border-gray-300 bg-white dark:bg-white text-gray-900 placeholder-gray-500 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
        />
        <textarea
          placeholder="Message"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          className="border border-gray-300 bg-white dark:bg-white text-gray-900 placeholder-gray-500 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
          rows={5}
          required
        />
        <button
          type="submit"
          disabled={status === "sending"}
          className="bg-indigo-600 hover:bg-indigo-700 text-white rounded-md px-4 py-2 font-semibold disabled:opacity-50 transition-colors"
        >
          Send
        </button>
      </form>
      {status === "sent" && (
        <p className="text-green-600 mt-4 text-center font-medium">
          Thank you for your feedback!
        </p>
      )}
      {status === "error" && (
        <p className="text-red-600 mt-4 text-center font-medium">
          Error sending your feedback.
        </p>
      )}
    </div>
  );
}
