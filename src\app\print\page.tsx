import { notFound } from "next/navigation";
import PrintClient from "../../components/PrintClient";

const ALLOWED_LAYOUTS = ["classic", "modern", "minimal"] as const;
type LayoutType = (typeof ALLOWED_LAYOUTS)[number];

function isLayoutType(value: string): value is LayoutType {
  return ALLOWED_LAYOUTS.includes(value as LayoutType);
}

export default async function PrintPage({ searchParams }: { searchParams: Promise<{ layout?: string }> }) {
  const { layout: layoutParam } = await searchParams;
  
  if (!layoutParam || !isLayoutType(layoutParam)) {
    return notFound();
  }

  return <PrintClient layout={layoutParam} />;
}
