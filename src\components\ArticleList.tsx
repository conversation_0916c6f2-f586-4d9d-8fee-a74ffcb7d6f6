'use client';
import type { Article } from '@/lib/db';
import Link from 'next/link';
import { useState, useEffect } from 'react';

interface Props {
  initialArticles: Article[];
}

export default function ArticleList({ initialArticles }: Props) {
  const [articles, setArticles] = useState(initialArticles);
  const [loading, setLoading] = useState(false);
  const [offset, setOffset] = useState(initialArticles.length);

  useEffect(() => {
    function onScroll() {
      if (loading) return;
      if (window.innerHeight + window.scrollY >= document.body.offsetHeight - 200) {
        loadMore();
      }
    }
    window.addEventListener('scroll', onScroll);
    return () => window.removeEventListener('scroll', onScroll);
  });

  async function loadMore() {
    setLoading(true);
    try {
      const res = await fetch(`/api/articles?offset=${offset}&limit=10`);
      if (res.ok) {
        const data = await res.json();
        setArticles([...articles, ...data.articles]);
        setOffset(offset + data.articles.length);
      }
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="flex flex-col gap-6">
      {articles.map((a) => (
        <Link key={a.id} href={`/blog/${a.slug}`} className="block p-4 rounded-lg bg-white dark:bg-gray-800 shadow hover:bg-indigo-50 dark:hover:bg-gray-700 transition-colors">
          <h2 className="text-2xl font-semibold mb-2">{a.title}</h2>
          <p className="text-gray-600 dark:text-gray-400 text-sm">{new Date(a.created_at).toLocaleDateString()}</p>
        </Link>
      ))}
      {loading && <p className="text-center text-gray-500 dark:text-gray-400">Loading...</p>}
    </div>
  );
}
