'use client';
import React, { useState } from "react";
import Link from "next/link";
import { useAuth } from "../contexts/AuthContext";

export default function Header() {
  const { user, signOut } = useAuth();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <header className="w-full py-4 px-6 flex items-center justify-between border-b border-indigo-200 bg-white/70 backdrop-blur-lg shadow-sm sticky top-0 z-30">
      <Link href="/" className="text-xl font-extrabold tracking-tight text-indigo-600 hover:text-indigo-800 transition-colors">
        CreateBillOnline
      </Link>

      {/* Desktop Navigation */}
      <nav className="hidden md:flex items-center gap-4 text-sm">
        <Link
          href="/blog"
          className="inline-block px-4 py-2 font-semibold text-indigo-700 border border-indigo-700 rounded-full hover:bg-indigo-700 hover:text-white transition-colors"
        >
          Blog
        </Link>
        <Link
          href="/feedback"
          className="inline-block px-4 py-2 font-semibold text-indigo-700 border border-indigo-700 rounded-full hover:bg-indigo-700 hover:text-white transition-colors"
        >
          Feedback
        </Link>
        <Link
          href="/csv-to-invoice"
          className="inline-block px-4 py-2 font-semibold text-indigo-700 border border-indigo-700 rounded-full hover:bg-indigo-700 hover:text-white transition-colors"
        >
          CSV to Invoice
        </Link>

        {/* Authentication Links */}
        {user ? (
          <div className="flex items-center gap-3">
            <Link
              href="/dashboard"
              className="inline-block px-4 py-2 font-semibold text-indigo-700 border border-indigo-700 rounded-full hover:bg-indigo-700 hover:text-white transition-colors"
            >
              Dashboard
            </Link>
            <Link
              href="/profile"
              className="inline-block px-4 py-2 font-semibold text-indigo-700 border border-indigo-700 rounded-full hover:bg-indigo-700 hover:text-white transition-colors"
            >
              Profile
            </Link>
            <button
              onClick={handleSignOut}
              className="inline-block px-4 py-2 font-semibold text-red-600 border border-red-600 rounded-full hover:bg-red-600 hover:text-white transition-colors"
            >
              Logout
            </button>
          </div>
        ) : (
          <div className="flex items-center gap-3">
            <Link
              href="/auth/login"
              className="inline-block px-4 py-2 font-semibold text-indigo-700 border border-indigo-700 rounded-full hover:bg-indigo-700 hover:text-white transition-colors"
            >
              Login
            </Link>
            <Link
              href="/auth/register"
              className="inline-block px-4 py-2 font-semibold bg-indigo-600 text-white border border-indigo-600 rounded-full hover:bg-indigo-700 transition-colors"
            >
              Register
            </Link>
          </div>
        )}

        <a
          href="https://x.com/dropcraftr"
          target="_blank"
          rel="noopener noreferrer"
          className="inline-block px-4 py-2 font-semibold text-gray-600 border border-gray-600 rounded-full hover:bg-gray-600 hover:text-white transition-colors"
        >
          Follow on X
        </a>
      </nav>

      {/* Mobile Menu Button */}
      <button
        className="md:hidden p-2 text-indigo-600"
        onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="absolute top-full left-0 right-0 bg-white/95 backdrop-blur-lg border-b border-indigo-200 md:hidden">
          <nav className="flex flex-col gap-2 p-4">
            <Link href="/blog" className="px-4 py-2 text-indigo-700 hover:bg-indigo-50 rounded-lg transition-colors">
              Blog
            </Link>
            <Link href="/feedback" className="px-4 py-2 text-indigo-700 hover:bg-indigo-50 rounded-lg transition-colors">
              Feedback
            </Link>
            <Link href="/csv-to-invoice" className="px-4 py-2 text-indigo-700 hover:bg-indigo-50 rounded-lg transition-colors">
              CSV to Invoice
            </Link>

            {user ? (
              <>
                <Link href="/dashboard" className="px-4 py-2 text-indigo-700 hover:bg-indigo-50 rounded-lg transition-colors">
                  Dashboard
                </Link>
                <Link href="/profile" className="px-4 py-2 text-indigo-700 hover:bg-indigo-50 rounded-lg transition-colors">
                  Profile
                </Link>
                <button
                  onClick={handleSignOut}
                  className="px-4 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors text-left"
                >
                  Logout
                </button>
              </>
            ) : (
              <>
                <Link href="/auth/login" className="px-4 py-2 text-indigo-700 hover:bg-indigo-50 rounded-lg transition-colors">
                  Login
                </Link>
                <Link href="/auth/register" className="px-4 py-2 bg-indigo-600 text-white hover:bg-indigo-700 rounded-lg transition-colors">
                  Register
                </Link>
              </>
            )}

            <a
              href="https://x.com/dropcraftr"
              target="_blank"
              rel="noopener noreferrer"
              className="px-4 py-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
            >
              Follow on X
            </a>
          </nav>
        </div>
      )}
    </header>
  );
}
