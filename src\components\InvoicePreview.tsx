import React from "react";
import dynamic from "next/dynamic";
import { InvoiceData } from "../store/invoiceStore";

const templates = {
  classic: dynamic(() => import("./templates/InvoiceClassic"), { ssr: false }),
  modern: dynamic(() => import("./templates/InvoiceModern"), { ssr: false }),
  minimal: dynamic(() => import("./templates/InvoiceMinimal"), { ssr: false }),
};

type Props = {
  data: InvoiceData;
  layout: "classic" | "modern" | "minimal";
};

export default function InvoicePreview({ data, layout }: Props) {
  const Template = templates[layout] || templates.classic;
  return (
    <div className="my-8 overflow-x-auto">
      <div className="mx-auto w-full sm:w-auto">
        <Template data={data} />
      </div>
    </div>
  );
}
