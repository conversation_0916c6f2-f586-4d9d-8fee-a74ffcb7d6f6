'use client';

import dynamic from 'next/dynamic';
import { InvoiceData } from '../store/invoiceStore';

const templates = {
  classic: dynamic(() => import('./templates/InvoiceClassic'), { ssr: false }),
  modern: dynamic(() => import('./templates/InvoiceModern'), { ssr: false }),
  minimal: dynamic(() => import('./templates/InvoiceMinimal'), { ssr: false }),
};

function getInvoiceData(): InvoiceData | null {
  if (typeof window === 'undefined') return null;
  // Data passed by Puppeteer via window object
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return (window as any).__INVOICE_DATA__ || null;
}

interface PrintClientProps {
  layout: 'classic' | 'modern' | 'minimal';
}

export default function PrintClient({ layout }: PrintClientProps) {
  const data = getInvoiceData();
  if (!data) {
    return <div>Errore: dati fattura mancanti.</div>;
  }
  const Template = templates[layout];
  return (
    <div className="bg-white w-[210mm] h-[297mm] mx-auto flex items-center justify-center">
      <Template data={data} />
    </div>
  );
}
