"use client";
import React, { useState, useRef } from "react";
import { useInvoiceStore } from "../store/invoiceStore";
import FieldStep from "./wizard/FieldStep";
import ServiceForm from "./wizard/ServiceForm";
import ServiceOverview from "./wizard/ServiceOverview";
import TaxStep from "./wizard/TaxStep";
import LogoStep from "./wizard/LogoStep";
import InvoiceDetailsStep from "./wizard/InvoiceDetailsStep";
import LayoutStep from "./wizard/LayoutStep";
import PreviewStep from "./wizard/PreviewStep";
import { LayoutType } from "./wizard/types";

const steps = [
  { key: "firstName", label: "Client first name", placeholder: "<PERSON>" },
  { key: "lastName", label: "Client last name", placeholder: "Doe" },
  { key: "companyName", label: "Client company name", placeholder: "John Doe LLC" },
  { key: "address", label: "Client address", placeholder: "123 Main St, London" },
  { key: "vat", label: "Client VAT number", placeholder: "GB123456789" },
  { key: "email", label: "Client email", placeholder: "<EMAIL>" },
  { key: "phone", label: "Client phone", placeholder: "+44 20 1234 5678" },
  // Company
  { key: "company_companyName", label: "Your company name (issuer)", placeholder: "Beautiful Invoices Ltd" },
  { key: "company_address", label: "Your company address", placeholder: "456 High St, Manchester" },
  { key: "company_vat", label: "Your company VAT number", placeholder: "GB987654321" },
  { key: "company_email", label: "Your company email", placeholder: "<EMAIL>" },
  { key: "company_phone", label: "Your company phone", placeholder: "+44 ************" },
];

export default function Wizard() {
  const [fieldIdx, setFieldIdx] = useState(0);
  const [serviceMode, setServiceMode] = useState(false);
  const [serviceDesc, setServiceDesc] = useState("");
  const [servicePrice, setServicePrice] = useState("");
  const [, setAddingService] = useState(false);
  const [taxMode, setTaxMode] = useState(false);
  const [taxValue, setTaxValue] = useState("");
  const [logoMode, setLogoMode] = useState(false);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [layoutMode, setLayoutMode] = useState(false);
  const [invoiceDetailsMode, setInvoiceDetailsMode] = useState(false);
  const [invoiceNumberInput, setInvoiceNumberInput] = useState("");
  const [invoiceDateInput, setInvoiceDateInput] = useState("");
  const [currencyInput, setCurrencyInput] = useState("€");
  const [selectedLayout, setSelectedLayout] = useState<LayoutType | null>(null);
  const [finished, setFinished] = useState(false);
  const fileInput = useRef<HTMLInputElement>(null);

  const { data, addClientData, addCompanyData, addService, setVatPercent, setLogo, setLayout, setInvoiceNumber, setInvoiceDate, setCurrency, reset } = useInvoiceStore();

  // Gestione step classico (campi cliente/azienda)
  function handleFieldSubmit(value: string) {
    const step = steps[fieldIdx];
    if (!step) return;
    const val = value.trim();
    if (step.key.startsWith("company_")) {
      addCompanyData({ [step.key.replace("company_", "")]: val });
    } else {
      addClientData({ [step.key]: val });
    }
    setFieldIdx(fieldIdx + 1);
  }

  // Servizi
  function handleServiceAdd(e: React.FormEvent) {
    e.preventDefault();
    if (!serviceDesc || !servicePrice) return;
    addService({ description: serviceDesc, price: parseFloat(servicePrice) });
    setServiceDesc("");
    setServicePrice("");
    setAddingService(false);
  }

  // Tasse
  function handleTaxSubmit(e: React.FormEvent) {
    e.preventDefault();
    setVatPercent(taxValue ? parseFloat(taxValue) : undefined);
    setTaxMode(false);
    setLogoMode(true);
  }

  // Dettagli fattura
  function handleInvoiceDetailsSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    setInvoiceNumber(invoiceNumberInput);
    setInvoiceDate(invoiceDateInput);
    setCurrency(currencyInput);
    setInvoiceDetailsMode(false);
    setLayoutMode(true);
  }

// Logo
  function handleLogoUpload(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onload = (evt) => {
      if (evt.target?.result && typeof evt.target.result === "string") {
        setLogo(evt.target.result);
        setLogoPreview(evt.target.result);
      }
    };
    reader.readAsDataURL(file);
  }

  // Layout
  function handleLayoutSelect(layout: LayoutType) {
    setLayout(layout);
    setSelectedLayout(layout);
    setLayoutMode(false);
    setFinished(true);
  }

  // Reset
  function handleRestart() {
    reset();
    setFieldIdx(0);
    setServiceMode(false);
    setServiceDesc("");
    setServicePrice("");
    setAddingService(false);
    setTaxMode(false);
    setTaxValue("");
    setLogoMode(false);
    setLogoPreview(null);
    setLayoutMode(false);
    setInvoiceDetailsMode(false);
    setInvoiceNumberInput("");
    setInvoiceDateInput("");
    setCurrencyInput("€");
    setSelectedLayout(null);
    setFinished(false);
  }

  // Rendering based on step state
  if (finished && selectedLayout) {
    return <PreviewStep data={data} layout={selectedLayout} onRestart={handleRestart} />;
  }

  if (layoutMode) {
    return <LayoutStep selected={selectedLayout} onSelect={handleLayoutSelect} />;
  }

  if (logoMode) {
    return (
      <LogoStep
        fileInput={fileInput}
        logoPreview={logoPreview}
        onUpload={handleLogoUpload}
        onProceed={() => { setLogoMode(false); setInvoiceDetailsMode(true); }}
      />
    );
  }

  if (invoiceDetailsMode) {
    return (
      <InvoiceDetailsStep
        invoiceNumber={invoiceNumberInput}
        invoiceDate={invoiceDateInput}
        currency={currencyInput}
        onInvoiceNumberChange={setInvoiceNumberInput}
        onInvoiceDateChange={setInvoiceDateInput}
        onCurrencyChange={setCurrencyInput}
        onSubmit={handleInvoiceDetailsSubmit}
        onSkip={() => { setInvoiceDetailsMode(false); setLayoutMode(true); }}
      />
    );
  }

  if (taxMode) {
    return (
      <TaxStep
        value={taxValue}
        onChange={setTaxValue}
        onSubmit={handleTaxSubmit}
        onSkip={() => { setTaxMode(false); setLogoMode(true); }}
      />
    );
  }

  if (serviceMode) {
    return (
      <ServiceForm
        services={data.services}
        description={serviceDesc}
        price={servicePrice}
        currency={data.currency || "€"}
        onDescriptionChange={setServiceDesc}
        onPriceChange={setServicePrice}
        onSubmit={handleServiceAdd}
        onClose={() => setServiceMode(false)}
      />
    );
  }

  if (fieldIdx < steps.length) {
    const step = steps[fieldIdx];
    return (
      <FieldStep
        step={step}
        onSubmit={handleFieldSubmit}
        onSkip={() => setFieldIdx(fieldIdx + 1)}
      />
    );
  }

  if (fieldIdx >= steps.length) {
    return (
      <ServiceOverview
        services={data.services}
        currency={data.currency || "€"}
        onAdd={() => setServiceMode(true)}
        onProceed={() => { if (data.services.length > 0) setTaxMode(true); }}
      />
    );
  }

  return null;
}
