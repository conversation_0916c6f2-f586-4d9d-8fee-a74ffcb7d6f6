'use client';
import React from 'react';

interface FieldOption {
  value: string;
  label: string;
}

interface Props {
  headers: string[];
  mapping: Record<string, string>;
  fields: FieldOption[];
  onChange: (header: string, value: string) => void;
  onNext: () => void;
}

export default function MappingStep({ headers, mapping, fields, onChange, onNext }: Props) {
  return (
    <div className="bg-white rounded-xl shadow px-6 py-8">
      <h2 className="text-xl font-semibold mb-4 text-indigo-700">Map fields</h2>
      <div className="space-y-4">
        {headers.map((h) => (
          <div key={h} className="flex items-center gap-4">
            <span className="w-40 text-sm font-medium">{h}</span>
            <select
              value={mapping[h] || ''}
              onChange={(e) => onChange(h, e.target.value)}
              className="border p-2 rounded flex-1"
            >
              {fields.map((f) => (
                <option key={f.value} value={f.value}>
                  {f.label}
                </option>
              ))}
            </select>
          </div>
        ))}
      </div>
      <div className="mt-6 text-right">
        <button
          className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
          onClick={onNext}
        >
          Next
        </button>
      </div>
    </div>
  );
}
