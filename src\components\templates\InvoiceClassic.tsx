import React from "react";
import type { Service } from "../../store/invoiceStore";
import { InvoiceData } from "../../store/invoiceStore";
import Image from "next/image";

type Props = {
  data: InvoiceData;
};

export default function InvoiceClassic({ data }: Props) {
  return (
    <div className="bg-white p-12 w-full sm:w-[210mm] sm:h-[297mm] mx-auto text-gray-900 font-serif rounded-xl shadow-xl border border-gray-100 flex flex-col justify-between overflow-hidden relative">
      {/* Decorative element */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-indigo-100 rounded-bl-full opacity-70" />
      
      {/* Header */}
      <div className="flex items-start justify-between border-b border-indigo-200 pb-8 mb-8 relative z-10">
        <div>
          <h1 className="text-4xl font-extrabold tracking-wider text-indigo-700 mb-4">INVOICE</h1>
          <div className="inline-flex gap-6 mb-4 p-2 bg-indigo-50 rounded-md">
            {data.invoiceNumber && (
              <span className="text-sm font-medium text-indigo-600">No. {data.invoiceNumber}</span>
            )}
            {data.invoiceDate && (
              <span className="text-sm font-medium text-indigo-600">Date: {data.invoiceDate}</span>
            )}
          </div>
          <div className="mt-4">
            <div className="text-base font-semibold text-gray-800">{data.company.companyName}</div>
            <div className="text-sm text-gray-600">{data.company.address}</div>
            <div className="text-sm text-gray-600">VAT: {data.company.vat}</div>
          </div>
        </div>
        {data.logo && (
          <div className="w-32 h-32 relative p-2 bg-white rounded-lg shadow-md">
            <Image src={data.logo} alt="Logo" fill className="object-contain" sizes="128px" />
          </div>
        )}
      </div>
      
      {/* Client info */}
      <div className="mb-8 p-6 bg-indigo-50 rounded-lg border-l-4 border-indigo-500">
        <div className="text-lg font-semibold text-indigo-700 mb-2">Recipient</div>
        <div className="font-medium text-gray-800 text-lg mb-1">
          {data.client.companyName || `${data.client.firstName || ""} ${data.client.lastName || ""}`}
        </div>
        <div className="grid grid-cols-2 gap-x-4 text-sm text-gray-600 mt-2">
          <div>{data.client.address}</div>
          <div>VAT: {data.client.vat}</div>
          <div>Email: {data.client.email}</div>
          <div>Phone: {data.client.phone}</div>
        </div>
      </div>
      
      {/* Services table */}
      <table className="w-full mb-8 divide-y divide-indigo-200 border-t border-b border-indigo-200">
        <thead className="bg-indigo-50">
          <tr>
            <th className="text-left py-3 px-4 font-semibold text-indigo-700">Description</th>
            <th className="text-right py-3 px-4 font-semibold text-indigo-700">Price</th>
          </tr>
        </thead>
        <tbody className="divide-y divide-indigo-100">
          {data.services.map((s: Service, i: number) => (
            <tr key={i} className={i % 2 === 0 ? "bg-white" : "bg-indigo-50/30"}>
              <td className="py-3 px-4 text-sm">{s.description}</td>
              <td className="py-3 px-4 text-right text-sm font-medium">{data.currency} {s.price.toFixed(2)}</td>
            </tr>
          ))}
        </tbody>
      </table>
      
      {/* Totals */}
      <div className="flex justify-end mb-6">
        <div className="w-64 p-4 rounded-lg border border-indigo-200 shadow-sm bg-white">
          <div className="flex justify-between text-sm py-2">
            <span className="text-gray-600">Subtotal</span>
            <span className="font-medium">{data.currency} {data.services.reduce((sum, s) => sum + s.price, 0).toFixed(2)}</span>
          </div>
          {data.vatPercent && (
            <div className="flex justify-between text-sm py-2 border-b border-dashed border-indigo-200">
              <span className="text-gray-600">VAT ({data.vatPercent}%)</span>
              <span className="font-medium">{data.currency} {(
                data.services.reduce((sum, s) => sum + s.price, 0) * (data.vatPercent / 100)
              ).toFixed(2)}</span>
            </div>
          )}
          <div className="flex justify-between font-bold text-lg mt-3 pt-2">
            <span className="text-indigo-700">Total</span>
            <span className="text-indigo-900">
              {data.currency} {(
                data.services.reduce((sum: number, s: Service) => sum + s.price, 0) * (1 + (data.vatPercent || 0) / 100)
              ).toFixed(2)}
            </span>
          </div>
        </div>
      </div>

      {/* Payment Information */}
      {data.paymentInfo && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h3 className="text-sm font-semibold text-indigo-700 mb-2">Payment Information</h3>
          <div className="text-xs text-gray-600 whitespace-pre-line">
            {data.paymentInfo}
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="text-center text-xs text-gray-500 mt-6 pt-3 border-t border-indigo-100">
        Thank you for your business
      </div>
    </div>
  );
}
