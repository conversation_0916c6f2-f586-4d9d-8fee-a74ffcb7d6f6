import React from "react";
import type { Service } from "../../store/invoiceStore";
import { InvoiceData } from "../../store/invoiceStore";
import Image from "next/image";

type Props = {
  data: InvoiceData;
};

export default function InvoiceMinimal({ data }: Props) {
  return (
    <div className="bg-white p-14 w-full sm:w-[210mm] sm:h-[297mm] mx-auto text-gray-900 font-mono rounded-none shadow-lg flex flex-col justify-between">
      {/* Header */}
      <div className="flex items-start justify-between mb-16 border-b border-gray-100 pb-6">
        <div>
          <span className="uppercase text-xs tracking-[0.25em] text-gray-400 font-semibold">INVOICE</span>
          <div className="mt-6 space-y-1">
            {data.invoiceNumber && (
              <div className="flex items-center">
                <span className="text-xs uppercase tracking-wider w-16 text-gray-400">No.</span>
                <span className="text-xs text-gray-700">{data.invoiceNumber}</span>
              </div>
            )}
            {data.invoiceDate && (
              <div className="flex items-center">
                <span className="text-xs uppercase tracking-wider w-16 text-gray-400">Date</span>
                <span className="text-xs text-gray-700">{data.invoiceDate}</span>
              </div>
            )}
          </div>
          <div className="mt-8">
            <div className="text-sm font-bold tracking-wide text-gray-800">{data.company.companyName}</div>
            <div className="text-xs text-gray-500 mt-1">{data.company.address}</div>
            <div className="text-xs text-gray-500">VAT: {data.company.vat}</div>
          </div>
        </div>
        {data.logo && (
          <div className="w-20 h-20 relative grayscale">
            <Image src={data.logo} alt="Logo" fill className="object-contain" sizes="80px" />
          </div>
        )}
      </div>

      {/* Client info */}
      <div className="mb-12">
        <div className="text-xs tracking-[0.25em] text-gray-400 uppercase mb-3">RECIPIENT</div>
        <div className="text-sm font-semibold tracking-wide mb-2">
          {data.client.companyName || `${data.client.firstName} ${data.client.lastName}`}
        </div>
        <div className="text-xs text-gray-500">{data.client.address}</div>
        <div className="grid grid-cols-2 gap-x-8 mt-3">
          <div className="text-xs text-gray-500">VAT: {data.client.vat}</div>
          <div className="text-xs text-gray-500">Email: {data.client.email}</div>
        </div>
      </div>

      {/* Services */}
      <table className="w-full mb-12">
        <thead>
          <tr className="border-b border-gray-200">
            <th className="text-left py-3 text-xs tracking-wider text-gray-400 uppercase font-normal">Description</th>
            <th className="text-right py-3 text-xs tracking-wider text-gray-400 uppercase font-normal">Price</th>
          </tr>
        </thead>
        <tbody>
          {data.services.map((s: Service, i: number) => (
            <tr key={i} className="border-b border-gray-50">
              <td className="py-3 text-sm text-gray-800">{s.description}</td>
              <td className="py-3 text-right text-sm text-gray-800 font-medium">{data.currency} {s.price.toFixed(2)}</td>
            </tr>
          ))}
        </tbody>
      </table>

      {/* Totals */}
      <div className="flex justify-end mb-12">
        <div className="w-48">
          <div className="flex justify-between text-xs text-gray-500 py-2">
            <span className="uppercase tracking-wider">Subtotal</span>
            <span>{data.currency} {data.services.reduce((sum: number, s: Service) => sum + s.price, 0).toFixed(2)}</span>
          </div>
          {data.vatPercent && (
            <div className="flex justify-between text-xs text-gray-500 py-2">
              <span className="uppercase tracking-wider">VAT ({data.vatPercent}%)</span>
              <span>{data.currency} {(
                data.services.reduce((sum: number, s: Service) => sum + s.price, 0) * (data.vatPercent / 100)
              ).toFixed(2)}</span>
            </div>
          )}
          <div className="flex justify-between font-bold text-sm mt-4 pt-3 border-t border-gray-200">
            <span className="uppercase tracking-wider">Total</span>
            <span>
              {data.currency} {(
                data.services.reduce((sum: number, s: Service) => sum + s.price, 0) * (1 + (data.vatPercent || 0) / 100)
              ).toFixed(2)}
            </span>
          </div>
        </div>
      </div>

      {/* Payment Information */}
      {data.paymentInfo && (
        <div className="mt-6 p-4 border-l-2 border-gray-300">
          <h3 className="text-xs font-medium text-gray-700 uppercase tracking-wider mb-2">
            Payment Information
          </h3>
          <div className="text-xs text-gray-600 whitespace-pre-line leading-relaxed">
            {data.paymentInfo}
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="mt-auto pt-4 border-t border-gray-100 flex justify-between items-center text-[10px] text-gray-400 uppercase tracking-widest">
        <span>Thank you for your business</span>
        <span>Page 1/1</span>
      </div>
    </div>
  );
}
