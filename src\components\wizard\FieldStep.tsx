'use client';
import React from 'react';

interface Step {
  key: string;
  label: string;
  placeholder: string;
}

interface Props {
  step: Step;
  onSubmit: (value: string) => void;
  onSkip: () => void;
}

export default function FieldStep({ step, onSubmit, onSkip }: Props) {
  return (
    <section className="w-full max-w-2xl mx-auto mt-12 bg-white/80 dark:bg-gray-800/60 backdrop-blur-lg rounded-3xl shadow-2xl px-10 py-12 flex flex-col items-center justify-center border border-gray-100 dark:border-gray-700">
      <h2 className="text-2xl font-semibold mb-6 text-center text-indigo-700">{step.label}</h2>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          const input = (e.currentTarget.elements[0] as HTMLInputElement).value;
          onSubmit(input);
          e.currentTarget.reset();
        }}
        className="flex flex-col gap-4 w-full max-w-xs mx-auto"
      >
        <input
          type="text"
          name={step.key}
          placeholder={step.placeholder}
          className="border rounded-lg px-4 py-3 text-lg focus:ring-2 focus:ring-indigo-400 shadow-sm"
          autoFocus
        />
        <button
          type="submit"
          className="px-5 py-2.5 rounded-full bg-indigo-600 text-white font-semibold hover:bg-indigo-700 shadow-md transition"
        >
          Proceed
        </button>
        <button
          type="button"
          className="px-5 py-2.5 rounded-full bg-gray-100 text-gray-700 font-semibold hover:bg-gray-200 shadow transition"
          onClick={onSkip}
        >
          Skip
        </button>
      </form>
    </section>
  );
}
