'use client';
import React from 'react';

interface Props {
  invoiceNumber: string;
  invoiceDate: string;
  currency: string;
  onInvoiceNumberChange: (val: string) => void;
  onInvoiceDateChange: (val: string) => void;
  onCurrencyChange: (val: string) => void;
  onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  onSkip: () => void;
}

export default function InvoiceDetailsStep({
  invoiceNumber,
  invoiceDate,
  currency,
  onInvoiceNumberChange,
  onInvoiceDateChange,
  onCurrencyChange,
  onSubmit,
  onSkip,
}: Props) {
  return (
    <section className="w-full max-w-2xl mx-auto mt-12 bg-white/80 dark:bg-gray-800/60 backdrop-blur-lg rounded-3xl shadow-2xl px-10 py-12 flex flex-col items-center justify-center border border-gray-100 dark:border-gray-700">
      <h2 className="text-2xl font-semibold mb-6 text-center text-indigo-700">Invoice details</h2>
      <form onSubmit={onSubmit} className="flex flex-col gap-4 w-full max-w-xs mx-auto">
        <input
          type="text"
          value={invoiceNumber}
          onChange={(e) => onInvoiceNumberChange(e.target.value)}
          placeholder="Invoice number"
          className="border rounded-lg px-4 py-3 text-lg focus:ring-2 focus:ring-indigo-400 shadow-sm"
          required
        />
        <input
          type="date"
          value={invoiceDate}
          onChange={(e) => onInvoiceDateChange(e.target.value)}
          className="border rounded-lg px-4 py-3 text-lg focus:ring-2 focus:ring-indigo-400 shadow-sm"
          required
        />
        <select
          value={currency}
          onChange={(e) => onCurrencyChange(e.target.value)}
          className="border rounded-lg px-4 py-3 text-lg focus:ring-2 focus:ring-indigo-400 shadow-sm"
        >
          <option value="€">EUR (€)</option>
          <option value="$">USD ($)</option>
          <option value="£">GBP (£)</option>
          <option value="CHF">CHF</option>
          <option value="¥">JPY (¥)</option>
          <option value="C$">CAD (C$)</option>
          <option value="A$">AUD (A$)</option>
          <option value="CN¥">CNY (¥)</option>
          <option value="₹">INR (₹)</option>
          <option value="R$">BRL (R$)</option>
        </select>
        <div className="flex gap-4 mt-4">
          <button
            type="submit"
            className="px-5 py-2.5 rounded-full bg-indigo-600 text-white font-semibold hover:bg-indigo-700 shadow-md transition"
          >
            Proceed
          </button>
          <button
            type="button"
            className="px-5 py-2.5 rounded-full bg-gray-100 text-gray-700 font-semibold hover:bg-gray-200 shadow transition"
            onClick={onSkip}
          >
            Skip
          </button>
        </div>
      </form>
    </section>
  );
}
