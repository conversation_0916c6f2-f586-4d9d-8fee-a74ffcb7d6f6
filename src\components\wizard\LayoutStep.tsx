'use client';
import React, { useState } from 'react';
import { LayoutType, ColorScheme, colorSchemes } from './types';

interface Props {
  selected: LayoutType | null;
  selectedColor?: ColorScheme;
  onSelect: (layout: LayoutType, colorScheme?: ColorScheme) => void;
  onProceed?: () => void;
}

const layoutPreviews = {
  classic: {
    name: 'Classic',
    description: 'Traditional and professional',
    preview: (colors: { primary: string; secondary: string; accent: string }) => (
      <div className="w-full h-24 bg-white border rounded-lg p-2 text-xs">
        <div className="flex justify-between items-start mb-2">
          <div className="w-8 h-2 rounded" style={{ backgroundColor: colors.primary }}></div>
          <div className="text-right">
            <div className="w-12 h-1 bg-gray-300 rounded mb-1"></div>
            <div className="w-8 h-1 bg-gray-300 rounded"></div>
          </div>
        </div>
        <div className="space-y-1">
          <div className="w-16 h-1 bg-gray-200 rounded"></div>
          <div className="w-12 h-1 bg-gray-200 rounded"></div>
        </div>
        <div className="mt-2 pt-2 border-t border-gray-200">
          <div className="flex justify-between">
            <div className="w-8 h-1 bg-gray-300 rounded"></div>
            <div className="w-6 h-1 rounded" style={{ backgroundColor: colors.secondary }}></div>
          </div>
        </div>
      </div>
    )
  },
  modern: {
    name: 'Modern',
    description: 'Clean and contemporary',
    preview: (colors: { primary: string; secondary: string; accent: string }) => (
      <div className="w-full h-24 bg-white border rounded-lg p-2 text-xs">
        <div className="flex items-center mb-2">
          <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: colors.primary }}></div>
          <div className="w-12 h-1 bg-gray-300 rounded"></div>
        </div>
        <div className="bg-gray-50 rounded p-1 mb-2">
          <div className="w-16 h-1 bg-gray-200 rounded mb-1"></div>
          <div className="w-10 h-1 bg-gray-200 rounded"></div>
        </div>
        <div className="flex justify-end">
          <div className="text-right">
            <div className="w-8 h-1 rounded mb-1" style={{ backgroundColor: colors.accent }}></div>
            <div className="w-6 h-1 rounded" style={{ backgroundColor: colors.secondary }}></div>
          </div>
        </div>
      </div>
    )
  },
  minimal: {
    name: 'Minimal',
    description: 'Simple and elegant',
    preview: (colors: { primary: string; secondary: string; accent: string }) => (
      <div className="w-full h-24 bg-white border rounded-lg p-2 text-xs">
        <div className="border-l-2 pl-2 mb-2" style={{ borderColor: colors.primary }}>
          <div className="w-12 h-1 bg-gray-300 rounded mb-1"></div>
          <div className="w-8 h-1 bg-gray-200 rounded"></div>
        </div>
        <div className="space-y-1 mb-2">
          <div className="w-14 h-1 bg-gray-100 rounded"></div>
          <div className="w-10 h-1 bg-gray-100 rounded"></div>
        </div>
        <div className="text-right">
          <div className="w-8 h-1 rounded" style={{ backgroundColor: colors.secondary }}></div>
        </div>
      </div>
    )
  }
};

export default function LayoutStep({ selected, selectedColor = 'blue', onSelect, onProceed }: Props) {
  const [activeLayout, setActiveLayout] = useState<LayoutType | null>(selected);
  const [activeColor, setActiveColor] = useState<ColorScheme>(selectedColor);

  const handleLayoutSelect = (layout: LayoutType) => {
    setActiveLayout(layout);
    // Automatically select the current color when layout is chosen
    onSelect(layout, activeColor);
  };

  const handleColorSelect = (color: ColorScheme) => {
    setActiveColor(color);
    if (activeLayout) {
      onSelect(activeLayout, color);
    }
  };

  return (
    <section className="w-full max-w-4xl mx-auto mt-12 bg-white/80 dark:bg-gray-800/60 backdrop-blur-lg rounded-3xl shadow-2xl px-6 py-8 sm:px-10 sm:py-12 flex flex-col items-center justify-center border border-gray-100 dark:border-gray-700">
      <h2 className="text-2xl font-semibold mb-6 text-center text-indigo-700">Choose Your Invoice Style</h2>

      {/* Layout Selection */}
      <div className="w-full mb-8">
        <h3 className="text-lg font-medium mb-4 text-center text-gray-700 dark:text-gray-300">Template</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {(Object.keys(layoutPreviews) as LayoutType[]).map((layout) => {
            const layoutInfo = layoutPreviews[layout];
            const colors = colorSchemes[activeColor];
            return (
              <button
                key={layout}
                className={`border-2 rounded-xl p-4 transition-all shadow-sm hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-400 ${
                  activeLayout === layout ? 'border-indigo-600 bg-indigo-50' : 'border-gray-200 bg-white'
                } hover:border-indigo-300`}
                onClick={() => handleLayoutSelect(layout)}
              >
                <div className="mb-3">
                  {layoutInfo.preview(colors)}
                </div>
                <h4 className="font-semibold text-gray-800 mb-1">{layoutInfo.name}</h4>
                <p className="text-xs text-gray-500">{layoutInfo.description}</p>
              </button>
            );
          })}
        </div>
      </div>

      {/* Color Scheme Selection */}
      {activeLayout && (
        <div className="w-full">
          <h3 className="text-lg font-medium mb-4 text-center text-gray-700 dark:text-gray-300">Color Scheme</h3>
          <div className="flex justify-center gap-4">
            {(Object.keys(colorSchemes) as ColorScheme[]).map((color) => {
              const scheme = colorSchemes[color];
              return (
                <button
                  key={color}
                  className={`flex flex-col items-center p-3 rounded-lg border-2 transition-all ${
                    activeColor === color ? 'border-gray-400 bg-gray-50' : 'border-gray-200 bg-white'
                  } hover:border-gray-300`}
                  onClick={() => handleColorSelect(color)}
                >
                  <div className="flex gap-1 mb-2">
                    <div className="w-4 h-4 rounded-full" style={{ backgroundColor: scheme.primary }}></div>
                    <div className="w-4 h-4 rounded-full" style={{ backgroundColor: scheme.secondary }}></div>
                    <div className="w-4 h-4 rounded-full" style={{ backgroundColor: scheme.accent }}></div>
                  </div>
                  <span className="text-xs font-medium text-gray-700">{scheme.name}</span>
                </button>
              );
            })}
          </div>

          {/* Proceed Button */}
          {onProceed && (
            <div className="mt-6 text-center">
              <button
                onClick={onProceed}
                className="px-6 py-3 bg-indigo-600 text-white font-semibold rounded-full hover:bg-indigo-700 shadow-md transition-colors"
              >
                Continue with {colorSchemes[activeColor].name} Theme
              </button>
            </div>
          )}
        </div>
      )}
    </section>
  );
}
