'use client';
import React from 'react';
import Image from 'next/image';

interface Props {
  fileInput: React.RefObject<HTMLInputElement | null>;
  logoPreview: string | null;
  onUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onProceed: () => void;
}

export default function LogoStep({ fileInput, logoPreview, onUpload, onProceed }: Props) {
  return (
    <section className="w-full max-w-2xl mx-auto mt-12 bg-white/80 dark:bg-gray-800/60 backdrop-blur-lg rounded-3xl shadow-2xl px-10 py-12 flex flex-col items-center justify-center border border-gray-100 dark:border-gray-700">
      <h2 className="text-2xl font-semibold mb-6 text-center text-indigo-700">Add a logo?</h2>
      <input
        type="file"
        accept="image/*"
        className="mb-4 text-sm file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:bg-indigo-600 file:text-white hover:file:bg-indigo-700"
        ref={fileInput}
        onChange={onUpload}
      />
      {logoPreview && (
        <div className="relative w-24 h-24 mb-2 rounded shadow overflow-hidden">
          <Image src={logoPreview} alt="Logo preview" fill className="object-contain" sizes="96px" />
        </div>
      )}
      <div className="flex gap-4 mt-4">
        <button
          className="px-5 py-2.5 rounded-full bg-indigo-600 text-white font-semibold hover:bg-indigo-700 shadow-md transition"
          onClick={onProceed}
        >
          Proceed without logo
        </button>
        {logoPreview && (
          <button
            className="px-5 py-2.5 rounded-full bg-green-600 text-white font-semibold hover:bg-green-700 shadow-md transition"
            onClick={onProceed}
          >
            Use this logo
          </button>
        )}
      </div>
    </section>
  );
}
