'use client';
import React, { useState } from 'react';

interface Props {
  initialValue?: string;
  onNext: (paymentInfo: string) => void;
  onBack: () => void;
}

export default function PaymentStep({ initialValue = '', onNext, onBack }: Props) {
  const [paymentInfo, setPaymentInfo] = useState(initialValue);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onNext(paymentInfo);
  };

  const handleSkip = () => {
    onNext('');
  };

  return (
    <section className="w-full max-w-2xl mx-auto mt-12 bg-white/80 dark:bg-gray-800/60 backdrop-blur-lg rounded-3xl shadow-2xl px-6 py-8 sm:px-10 sm:py-12 flex flex-col items-center justify-center border border-gray-100 dark:border-gray-700">
      <h2 className="text-2xl font-semibold mb-6 text-center text-indigo-700">Payment Information</h2>
      <p className="text-gray-600 dark:text-gray-300 text-center mb-8 max-w-md">
        Add payment instructions, bank details, or other payment-related information that will appear on your invoice.
      </p>
      
      <form onSubmit={handleSubmit} className="w-full max-w-md space-y-6">
        <div>
          <label htmlFor="paymentInfo" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Payment Details
          </label>
          <textarea
            id="paymentInfo"
            value={paymentInfo}
            onChange={(e) => setPaymentInfo(e.target.value)}
            placeholder="Enter payment instructions, bank account details, payment terms, etc.&#10;&#10;Example:&#10;Bank: Example Bank&#10;Account: **********&#10;IBAN: DE89 3704 0044 0532 0130 00&#10;Payment due within 30 days"
            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none"
            rows={8}
          />
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3 justify-between">
          <button
            type="button"
            onClick={onBack}
            className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors font-medium"
          >
            Back
          </button>
          
          <div className="flex gap-3">
            <button
              type="button"
              onClick={handleSkip}
              className="px-6 py-3 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors font-medium"
            >
              Skip
            </button>
            <button
              type="submit"
              className="px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors font-medium shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
            >
              Continue
            </button>
          </div>
        </div>
      </form>
      
      <div className="mt-6 text-xs text-gray-500 dark:text-gray-400 text-center max-w-md">
        <p>This information will be displayed on your invoice. You can always edit or remove it later.</p>
      </div>
    </section>
  );
}
