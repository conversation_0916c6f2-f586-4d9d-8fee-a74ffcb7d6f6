'use client';
import React from 'react';
import { InvoiceData } from '../../store/invoiceStore';
import { LayoutType } from './types';
import LoadingOverlay from '../LoadingOverlay';
import { useAuth } from '../../contexts/AuthContext';
import { db } from '../../lib/supabase';

interface Props {
  data: InvoiceData;
  layout: LayoutType;
  onRestart: () => void;
}

export default function PreviewStep({ data, layout, onRestart }: Props) {
  const [isGenerating, setIsGenerating] = React.useState(false);
  const [isSaving, setIsSaving] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [saveSuccess, setSaveSuccess] = React.useState(false);
  const { user } = useAuth();
  const InvoicePreview = React.lazy(() => import('../InvoicePreview'));

  const handleSaveInvoice = async () => {
    if (!user) {
      setError('You must be logged in to save invoices');
      return;
    }

    setIsSaving(true);
    setError(null);
    setSaveSuccess(false);

    try {
      // Generate a title for the invoice
      const clientName = data.client.firstName && data.client.lastName
        ? `${data.client.firstName} ${data.client.lastName}`
        : data.client.companyName || 'Client';
      const invoiceTitle = `Invoice for ${clientName}${data.invoiceNumber ? ` - ${data.invoiceNumber}` : ''}`;

      // Save the invoice data
      await db.saveInvoice(invoiceTitle, {
        ...data,
        layout,
        createdAt: new Date().toISOString()
      });

      setSaveSuccess(true);
      setTimeout(() => setSaveSuccess(false), 3000); // Hide success message after 3 seconds
    } catch (err: unknown) {
      const e = err instanceof Error ? err : new Error(String(err));
      console.error('Save invoice error:', e);
      setError(e.message);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <>
      {isGenerating && <LoadingOverlay />}
      <section className="w-full max-w-3xl mx-auto mt-12 bg-white/80 dark:bg-gray-800/60 backdrop-blur-lg rounded-3xl shadow-2xl px-6 py-8 sm:px-10 sm:py-12 flex flex-col items-center justify-center border border-gray-100 dark:border-gray-700 overflow-x-auto">
        <h2 className="text-2xl font-semibold mb-4 text-center text-indigo-700">Preview your invoice</h2>
        <p className="text-gray-500 text-center mb-6">Check the final result, then download the PDF.</p>
        <React.Suspense fallback={<div>Loading preview...</div>}>
          <InvoicePreview data={data} layout={layout} />
        </React.Suspense>
      {error && <p className="text-red-500 text-sm mt-2">{error}</p>}
      {saveSuccess && <p className="text-green-600 text-sm mt-2">Invoice saved successfully!</p>}
      <div className="flex flex-wrap gap-4 mt-6 justify-center">
        {user && (
          <button
            className="px-5 py-2.5 rounded-full bg-green-600 text-white font-semibold hover:bg-green-700 shadow-md transition flex items-center justify-center min-w-[140px]"
            onClick={handleSaveInvoice}
            disabled={isSaving}
          >
            {isSaving ? (
              <span className="flex items-center gap-2">
                <span className="w-4 h-4 border-2 border-white border-t-transparent animate-spin rounded-full"></span>
                Saving...
              </span>
            ) : (
              'Save Invoice'
            )}
          </button>
        )}
        <button
          className="px-5 py-2.5 rounded-full bg-indigo-600 text-white font-semibold hover:bg-indigo-700 shadow-md transition flex items-center justify-center min-w-[140px]"
          onClick={async () => {
            setIsGenerating(true);
            setError(null);
            try {
              const res = await fetch('/api/generate', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ data, layout }),
              });
              if (!res.ok) {
                let msg = 'PDF generation error';
                try {
                  const err = await res.json();
                  if (err && err.details) msg = `PDF generation error: ${err.details}`;
                } catch {}
                throw new Error(msg);
              }
              const blob = await res.blob();
              const url = window.URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = 'invoice.pdf';
              document.body.appendChild(a);
              a.click();
              setTimeout(() => {
                window.URL.revokeObjectURL(url);
                a.remove();
              }, 200);
            } catch (err: unknown) {
              const e = err instanceof Error ? err : new Error(String(err));
              console.error('PDF generation error:', e);
              setError(e.message);
            } finally {
              setIsGenerating(false);
            }
          }}
          disabled={isGenerating}
        >
          {isGenerating ? (
            <span className="flex items-center gap-2"><span className="w-4 h-4 border-2 border-white border-t-transparent animate-spin rounded-full"></span> Generating PDF...</span>
          ) : (
            'Download PDF'
          )}
        </button>
        <button
          className="px-5 py-2.5 rounded-full bg-gray-100 text-gray-700 font-semibold hover:bg-gray-200 shadow transition"
          onClick={onRestart}
        >
          Create a new invoice
        </button>
      </div>
      </section>
    </>
  );
}
