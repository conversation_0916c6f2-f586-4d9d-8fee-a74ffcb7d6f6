'use client';
import React from 'react';
import type { Service } from '../../store/invoiceStore';

interface Props {
  services: Service[];
  description: string;
  price: string;
  currency: string;
  onDescriptionChange: (val: string) => void;
  onPriceChange: (val: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  onClose: () => void;
}

export default function ServiceForm({
  services,
  description,
  price,
  currency,
  onDescriptionChange,
  onPriceChange,
  onSubmit,
  onClose,
}: Props) {
  return (
    <section className="w-full max-w-2xl mx-auto mt-12 bg-white/80 dark:bg-gray-800/60 backdrop-blur-lg rounded-3xl shadow-2xl px-10 py-12 flex flex-col items-center justify-center border border-gray-100 dark:border-gray-700">
      <h2 className="text-2xl font-semibold mb-6 text-center text-indigo-700">Add a service</h2>
      <form onSubmit={onSubmit} className="flex flex-col gap-4 w-full max-w-xs mx-auto">
        <input
          type="text"
          value={description}
          onChange={(e) => onDescriptionChange(e.target.value)}
          placeholder="Description (e.g. Consultation)"
          className="border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 text-lg text-gray-900 dark:text-white bg-white dark:bg-gray-700 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-indigo-400 shadow-sm"
          required
        />
        <input
          type="number"
          value={price}
          onChange={(e) => onPriceChange(e.target.value)}
          placeholder="Price (e.g. 100.00)"
          min="0"
          step="0.01"
          className="border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 text-lg text-gray-900 dark:text-white bg-white dark:bg-gray-700 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-indigo-400 shadow-sm"
          required
        />
        <button
          type="submit"
          className="px-5 py-2.5 rounded-full bg-indigo-600 text-white font-semibold hover:bg-indigo-700 shadow-md transition"
        >
          Add service
        </button>
      </form>
      <div className="flex flex-col gap-2 mt-6 w-full">
        {services.length > 0 && (
          <>
            <h3 className="text-base font-semibold mb-1">Added services:</h3>
            <ul className="list-disc ml-6 text-gray-700 text-sm">
              {services.map((s, i) => (
                <li key={i}>{s.description} - {currency} {s.price.toFixed(2)}</li>
              ))}
            </ul>
          </>
        )}
      </div>
      <div className="flex gap-4 mt-6">
        <button
          className="px-5 py-2.5 rounded-full bg-green-600 text-white font-semibold hover:bg-green-700 shadow-md transition"
          onClick={onClose}
          disabled={services.length === 0}
        >
          Proceed
        </button>
        <button
          className="px-5 py-2.5 rounded-full bg-gray-100 text-gray-700 font-semibold hover:bg-gray-200 shadow transition"
          onClick={onClose}
        >
          Cancel
        </button>
      </div>
    </section>
  );
}
