'use client';
import React from 'react';
import type { Service } from '../../store/invoiceStore';

interface Props {
  services: Service[];
  onAdd: () => void;
  onProceed: () => void;
  currency: string;
}

export default function ServiceOverview({ services, onAdd, onProceed, currency }: Props) {
  return (
    <section className="w-full max-w-2xl mx-auto mt-12 bg-white/80 dark:bg-gray-800/60 backdrop-blur-lg rounded-3xl shadow-2xl px-10 py-12 flex flex-col items-center justify-center border border-gray-100 dark:border-gray-700">
      <h2 className="text-2xl font-semibold mb-6 text-center text-indigo-700">Your services</h2>
      <p className="text-gray-500 text-center mb-4">Add at least one service to invoice.</p>
      <div className="flex flex-col gap-2 mb-4 w-full">
        {services.length > 0 && (
          <>
            <h3 className="text-base font-semibold mb-1">Added services:</h3>
            <ul className="list-disc ml-6 text-gray-700 text-sm">
              {services.map((s, i) => (
                <li key={i}>{s.description} - {currency} {s.price.toFixed(2)}</li>
              ))}
            </ul>
          </>
        )}
      </div>
      <div className="flex gap-4 mt-6">
        <button
          className="px-5 py-2.5 rounded-full bg-indigo-600 text-white font-semibold hover:bg-indigo-700 shadow-md transition"
          onClick={onAdd}
        >
          Add service
        </button>
        <button
          className="px-5 py-2.5 rounded-full bg-green-600 text-white font-semibold hover:bg-green-700 shadow-md transition"
          onClick={onProceed}
          disabled={services.length === 0}
        >
          Proceed
        </button>
      </div>
    </section>
  );
}
