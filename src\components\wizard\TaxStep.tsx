'use client';
import React from 'react';

interface Props {
  value: string;
  onChange: (val: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  onSkip: () => void;
}

export default function TaxStep({ value, onChange, onSubmit, onSkip }: Props) {
  return (
    <section className="w-full max-w-2xl mx-auto mt-12 bg-white/80 dark:bg-gray-800/60 backdrop-blur-lg rounded-3xl shadow-2xl px-10 py-12 flex flex-col items-center justify-center border border-gray-100 dark:border-gray-700">
      <h2 className="text-2xl font-semibold mb-6 text-center text-indigo-700">Need to apply taxes?</h2>
      <form onSubmit={onSubmit} className="flex flex-col gap-4 w-full max-w-xs mx-auto">
        <input
          type="number"
          min="0"
          max="100"
          step="0.01"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder="Percentage (e.g. 22)"
          className="border rounded-lg px-4 py-3 text-lg focus:ring-2 focus:ring-indigo-400 shadow-sm"
        />
        <button
          type="submit"
          className="px-5 py-2.5 rounded-full bg-indigo-600 text-white font-semibold hover:bg-indigo-700 shadow-md transition"
        >
          Proceed
        </button>
        <button
          type="button"
          className="px-5 py-2.5 rounded-full bg-gray-100 text-gray-700 font-semibold hover:bg-gray-200 shadow transition"
          onClick={onSkip}
        >
          Skip
        </button>
      </form>
    </section>
  );
}
