export type LayoutType = 'classic' | 'modern' | 'minimal';

export type ColorScheme = 'blue' | 'green' | 'purple';

export interface LayoutOption {
  type: LayoutType;
  colorScheme: ColorScheme;
  name: string;
  description: string;
  preview: string;
}

export const colorSchemes: Record<ColorScheme, { primary: string; secondary: string; accent: string; name: string }> = {
  blue: {
    primary: '#3B82F6',
    secondary: '#1E40AF',
    accent: '#DBEAFE',
    name: 'Ocean Blue'
  },
  green: {
    primary: '#10B981',
    secondary: '#047857',
    accent: '#D1FAE5',
    name: '<PERSON> Green'
  },
  purple: {
    primary: '#8B5CF6',
    secondary: '#7C3AED',
    accent: '#EDE9FE',
    name: '<PERSON> Purple'
  }
};
