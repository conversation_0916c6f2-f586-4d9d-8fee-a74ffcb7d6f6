import { createClient } from '@supabase/supabase-js';

export type Article = {
  id: number;
  title: string;
  slug: string;
  content: string;
  created_at: string;
  image_url?: string;
  excerpt?: string;
};

// Create Supabase client with service role key for server-side operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

export async function getArticles(offset = 0, limit = 10): Promise<Article[]> {
  const { data, error } = await supabase
    .from('articles')
    .select('id, title, slug, content, created_at, image_url, excerpt')
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    throw new Error(`Failed to fetch articles: ${error.message}`);
  }

  return data || [];
}

export async function getArticleBySlug(slug: string): Promise<Article | null> {
  const { data, error } = await supabase
    .from('articles')
    .select('id, title, slug, content, created_at, image_url, excerpt')
    .eq('slug', slug)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // No rows returned
      return null;
    }
    throw new Error(`Failed to fetch article: ${error.message}`);
  }

  return data;
}

export async function getLatestArticles(limit = 3, excludeId?: number): Promise<Article[]> {
  let query = supabase
    .from('articles')
    .select('id, title, slug, content, created_at, image_url, excerpt')
    .order('created_at', { ascending: false })
    .limit(limit);

  if (excludeId) {
    query = query.neq('id', excludeId);
  }

  const { data, error } = await query;

  if (error) {
    throw new Error(`Failed to fetch latest articles: ${error.message}`);
  }

  return data || [];
}

export async function createArticle(
  title: string,
  slug: string,
  content: string,
  excerpt?: string,
  imageUrl?: string
): Promise<Article> {
  const { data, error } = await supabase
    .from('articles')
    .insert({
      title,
      slug,
      content,
      excerpt: excerpt || null,
      image_url: imageUrl || null
    })
    .select('id, title, slug, content, created_at, image_url, excerpt')
    .single();

  if (error) {
    throw new Error(`Failed to create article: ${error.message}`);
  }

  return data;
}
