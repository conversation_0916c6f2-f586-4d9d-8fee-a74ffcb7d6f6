import { marked } from 'marked';
import hljs from 'highlight.js';
import DOMPurify from 'dompurify';

// Basic HTML sanitizer for server-side use
function sanitizeHtml(html: string): string {
  return html
    .replace(/<script[\s\S]*?>[\s\S]*?<\/script>/gi, '')
    .replace(/on\w+\s*=\s*("[^"]*"|'[^']*')/gi, '')
    .replace(/javascript:/gi, '');
}

// Estendi marked.MarkedOptions con il tipo Renderer
declare module 'marked' {
  interface MarkedOptions {
    highlight?: (code: string, lang: string) => string;
  }
}

// Configura marked per utilizzare highlight.js
marked.setOptions({
  highlight: (code: string, lang: string) => {
    try {
      if (lang && hljs.getLanguage(lang)) {
        return hljs.highlight(code, { language: lang }).value;
      } else {
        return hljs.highlightAuto(code).value;
      }
    } catch (error) {
      console.error('Error highlighting code:', error);
      return code;
    }
  }
});

/**
 * Converts markdown content to styled HTML for blog articles
 * This function wraps the generated HTML with custom CSS classes
 * to enhance the styling of blog content
 */
export function markdownToHtml(md: string): string {
  // Basic markdown parsing
  const rawHtml = marked.parse(md) as string;

  // Sanitize the HTML to prevent XSS attacks
  const sanitizedHtml =
    typeof window !== 'undefined'
      ? DOMPurify.sanitize(rawHtml)
      : sanitizeHtml(rawHtml);
  
  // Apply custom styling wrappers around specific HTML elements
  const enhancedHtml = applyCustomStyling(sanitizedHtml);
  
  return enhancedHtml;
}

/**
 * Applies custom styling to HTML elements using regular expressions
 */
function applyCustomStyling(html: string): string {
  // Style headings with proper spacing and font sizes
  const styled = html
    .replace(/<h1>(.*?)<\/h1>/g, '<h1 class="text-3xl font-bold mt-8 mb-4">$1</h1>')
    .replace(/<h2>(.*?)<\/h2>/g, '<h2 class="text-2xl font-bold mt-6 mb-3">$1</h2>')
    .replace(/<h3>(.*?)<\/h3>/g, '<h3 class="text-xl font-semibold mt-5 mb-2">$1</h3>')
    .replace(/<h4>(.*?)<\/h4>/g, '<h4 class="text-lg font-semibold mt-4 mb-1">$1</h4>')
    .replace(/<h5>(.*?)<\/h5>/g, '<h5 class="text-base font-semibold mt-3 mb-1">$1</h5>')
    .replace(/<h6>(.*?)<\/h6>/g, '<h6 class="text-sm font-semibold mt-3 mb-1">$1</h6>')
    
    // Style links
    .replace(/<a href="(.*?)">(.*?)<\/a>/g, 
      '<a href="$1" class="text-indigo-600 hover:text-indigo-800 hover:underline">$2</a>')
    
    // Enhance lists
    .replace(/<ul>/g, '<ul class="list-disc pl-8 mb-6 space-y-2">')
    .replace(/<ol>/g, '<ol class="list-decimal pl-8 mb-6 space-y-2">')
    .replace(/<li>/g, '<li class="ml-2">')
    
    // Per code blocks con linguaggio specifico (già gestiti da highlight.js)
    .replace(/<pre><code class="language-(.*?)">(.*?)<\/code><\/pre>/g, 
      '<pre class="rounded-md bg-gray-800 p-4 my-6 overflow-x-auto"><code class="language-$1 text-sm">$2</code></pre>')
    
    // Per code blocks senza linguaggio specificato
    .replace(/<pre><code>((?!class="language).*?)<\/code><\/pre>/g, 
      '<pre class="rounded-md bg-gray-800 p-4 my-6 overflow-x-auto"><code class="text-sm text-gray-200">$1</code></pre>')
    
    // Style inline code
    .replace(/<code>(.*?)<\/code>/g,
      '<code class="bg-gray-100 dark:bg-gray-700 text-pink-600 dark:text-pink-400 px-1 py-0.5 rounded text-sm">$1</code>')
      
    // Add spacing for images and wrap in figure
    .replace(/<img src="(.*?)" alt="(.*?)">/g, 
      '<figure class="my-6"><img src="$1" alt="$2" class="mx-auto rounded-lg shadow-md" /></figure>')
    
    // Add styles to tables
    .replace(/<table>/g, '<div class="overflow-x-auto my-6"><table class="min-w-full bg-white border border-gray-300">')
    .replace(/<\/table>/g, '</table></div>')
    .replace(/<thead>/g, '<thead class="bg-gray-100">')
    .replace(/<th>/g, '<th class="py-2 px-4 border-b border-gray-300 text-left">')
    .replace(/<td>/g, '<td class="py-2 px-4 border-b border-gray-200">')
    
    // Add horizontal rule styling
    .replace(/<hr>/g, '<hr class="my-8 border-gray-300" />');

  return styled;
}
