import puppeteer, { <PERSON><PERSON><PERSON> } from 'puppeteer-core';
import chromium from '@sparticuz/chromium';
import { InvoiceData } from '../../store/invoiceStore';

declare global {
  interface Window {
    __INVOICE_DATA__: InvoiceData;
  }
}

/**
 * Genera un PDF da una pagina Next.js (route /print) usando Puppeteer.
 * @param data Dati della fattura
 * @param layout Layout della fattura (classic, modern, minimal)
 * @returns Buffer PDF
 */
export async function generatePdf(data: InvoiceData, layout: string): Promise<Uint8Array> {
  // In prod, puoi usare @sparticuz/chromium (Vercel/AWS) oppure path custom
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
  const url = `${baseUrl}/print?layout=${layout}`;

  const isProd = process.env.NODE_ENV === 'production';
  const executablePath = isProd
    ? await chromium.executablePath()
    : process.env.CHROME_EXECUTABLE_PATH;

  let browser: Browser | null = null;
  try {
    browser = await puppeteer.launch({
      args: isProd ? chromium.args : ['--no-sandbox', '--disable-setuid-sandbox'],
      executablePath: executablePath as string,
      headless: isProd ? chromium.headless : true,
    });
    const page = await browser.newPage();
    // Inietta i dati lato client prima del caricamento della pagina
    await page.evaluateOnNewDocument((invoiceData: InvoiceData) => {
      window.__INVOICE_DATA__ = invoiceData;
    }, data);
    await page.goto(url, { waitUntil: 'networkidle0' });
    // Attendi che il client component sia montato
    await page.waitForSelector('.bg-white');
    const pdfBuffer: Uint8Array = await page.pdf({ format: 'A4', printBackground: true });
    return pdfBuffer;
  } catch (e: unknown) {
    const error = e instanceof Error ? e : new Error(String(e));
    console.error('[generatePdf] PDF generation failed', error);
    throw new Error(error.message);
  } finally {
    if (browser) {
      try {
        await browser.close();
      } catch (closeErr) {
        console.error('[generatePdf] Failed to close browser', closeErr);
      }
    }
  }
}
