import { createClient, SupabaseClient } from '@supabase/supabase-js';

let supabaseInstance: SupabaseClient | null = null;

function getSupabaseClient() {
  if (typeof window === 'undefined') {
    // Server-side: return null to avoid URL validation issues during build
    return null;
  }

  if (!supabaseInstance) {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (supabaseUrl && supabaseAnonKey && supabaseUrl !== 'your_supabase_project_url') {
      try {
        supabaseInstance = createClient(supabaseUrl, supabaseAnonKey);
      } catch (error) {
        console.warn('Failed to initialize Supabase client:', error);
      }
    }
  }

  return supabaseInstance;
}

export const supabase = getSupabaseClient();

// Types for our database
export type User = {
  id: string;
  email: string;
  created_at: string;
  updated_at: string;
};

export type SavedInvoice = {
  id: string;
  user_id: string;
  title: string;
  invoice_data: Record<string, unknown>; // JSON data
  created_at: string;
  updated_at: string;
};

export type UserProfile = {
  id: string;
  user_id: string;
  full_name?: string;
  company_name?: string;
  address?: string;
  phone?: string;
  created_at: string;
  updated_at: string;
};

// Auth helper functions
export const auth = {
  signUp: async (email: string, password: string) => {
    const client = getSupabaseClient();
    if (!client) throw new Error('Supabase not configured');
    return await client.auth.signUp({ email, password });
  },

  signIn: async (email: string, password: string) => {
    const client = getSupabaseClient();
    if (!client) throw new Error('Supabase not configured');
    return await client.auth.signInWithPassword({ email, password });
  },

  signOut: async () => {
    const client = getSupabaseClient();
    if (!client) throw new Error('Supabase not configured');
    return await client.auth.signOut();
  },

  getUser: async () => {
    const client = getSupabaseClient();
    if (!client) throw new Error('Supabase not configured');
    return await client.auth.getUser();
  },

  getSession: async () => {
    const client = getSupabaseClient();
    if (!client) throw new Error('Supabase not configured');
    return await client.auth.getSession();
  }
};

// Database helper functions
export const db = {
  // Invoice operations
  saveInvoice: async (title: string, invoiceData: Record<string, unknown>) => {
    const client = getSupabaseClient();
    if (!client) throw new Error('Supabase not configured');
    const { data: { user } } = await client.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    return await client
      .from('saved_invoices')
      .insert({
        user_id: user.id,
        title,
        invoice_data: invoiceData
      })
      .select()
      .single();
  },

  getUserInvoices: async () => {
    const client = getSupabaseClient();
    if (!client) throw new Error('Supabase not configured');
    const { data: { user } } = await client.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    return await client
      .from('saved_invoices')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });
  },

  deleteInvoice: async (invoiceId: string) => {
    const client = getSupabaseClient();
    if (!client) throw new Error('Supabase not configured');
    const { data: { user } } = await client.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    return await client
      .from('saved_invoices')
      .delete()
      .eq('id', invoiceId)
      .eq('user_id', user.id);
  },

  // Profile operations
  getProfile: async () => {
    const client = getSupabaseClient();
    if (!client) throw new Error('Supabase not configured');
    const { data: { user } } = await client.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    return await client
      .from('user_profiles')
      .select('*')
      .eq('user_id', user.id)
      .single();
  },

  updateProfile: async (profileData: Partial<UserProfile>) => {
    const client = getSupabaseClient();
    if (!client) throw new Error('Supabase not configured');
    const { data: { user } } = await client.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    return await client
      .from('user_profiles')
      .upsert({
        user_id: user.id,
        ...profileData,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
  }
};
